<template>
  <div class="status-bar-placeholder"></div>
  <view class="ai-chat-page">
    <l-message-list ref="messageListRef" :messages="messages" :ai-state="aiState" @task-confirm="handleTaskConfirm"
      @task-cancel="handleTaskCancel" />
    <view class="message-input-wrapper">
      <z-message-input :showMicButton="false" v-model="inputValue" @send="handleSendMessageStream"
        @send-audio="handleSendAudio" @stop="handleStop" placeholder="有什么想问的..." cloud-path="aiAssistant/"
        :processing="aiState.isProcessing || aiState.streaming.active"
        :disabled="aiState.isProcessing || aiState.streaming.active">
        <template #toolbar>
          <div class="toolbar-btn" @click="goToHome">
            <i class="fas fa-home"></i>
            <span class="label">首页</span>
          </div>
          <div class="toolbar-btn" @click="clearChatHistory">
            <i class="fas fa-trash-alt"></i>
            <span class="label">清空聊天记录</span>
          </div>
          <div class="toolbar-btn">
            <i class="fas fa-comment-dots"></i>
            <span class="label">碎碎念</span>
          </div>
          <div class="toolbar-btn">
            <i class="fas fa-list-check"></i>
            <span class="label">今日任务预览</span>
          </div>
          <div class="toolbar-btn">
            <i class="fas fa-clipboard-check"></i>
            <span class="label">今日任务总结</span>
          </div>
        </template>
      </z-message-input>
      <!-- 确认清空弹窗（美化版） -->
      <z-confirm-modal v-model:visible="showClearConfirm" title="清空聊天记录" message="确定要清空当前会话的全部消息吗？此操作不可恢复。"
        confirm-text="清空" cancel-text="取消" type="danger" @confirm="doClearChatHistory" />
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import LMessageList from './components/l-message-list.vue'
import ZMessageInput from '@/components/z-message-input/z-message-input.vue'
import ZConfirmModal from '@/components/z-confirm-modal.vue'
import { router } from '@/utils/tools'

const inputValue = ref('')
const messageListRef = ref(null)
const currentChannel = ref(null)
const lastClientReqId = ref('')
const hasFirstMessage = ref(false)
let firstMessageTimer = null

// 消息类型定义（需要在使用前定义）
const MESSAGE_TYPES = {
  USER: 'user', // 用户消息
  AI_STREAMING: 'ai_streaming', // AI 流式消息
  AI_COMPLETE: 'ai_complete', // AI 完成消息
  TOOL_RESULT: 'tool_result', // 工具执行结果消息（新增）
  ERROR: 'error', // 错误消息
}

const MESSAGE_STATUS = {
  SENDING: 'sending', // 发送中
  STREAMING: 'streaming', // 流式显示中
  COMPLETE: 'complete', // 完成
  ERROR: 'error', // 错误
}

// Function Calling 专用 SSE 消息类型（与后端保持一致）
const SSE_MESSAGE_TYPES = {
  // === 基础流程消息 ===
  PROCESSING_START: 'processing_start', // 开始处理用户请求
  SESSION_END: 'session_end', // 会话结束
  ERROR: 'error', // 系统错误

  // === 聊天内容消息 ===
  CHAT_CONTENT_CHUNK: 'chat_content_chunk', // 流式聊天内容块

  // === Function Calling 专用消息 ===
  TOOL_CALL_START: 'tool_call_start', // 工具调用开始
  TOOL_EXECUTION_START: 'tool_execution_start', // 工具执行开始
  TOOL_EXECUTION_COMPLETE: 'tool_execution_complete', // 工具执行完成
  TOOL_EXECUTION_ERROR: 'tool_execution_error', // 工具执行失败
  TOOL_RESULT_PROCESSING: 'tool_result_processing', // 工具结果处理中
  TOOL_RESULT_ERROR: 'tool_result_error', // 工具结果处理失败
}

const messages = ref([
  {
    _id: 'welcome_1',
    content: '你好！我是你的 AI 助手，有什么可以帮助你的吗？',
    type: MESSAGE_TYPES.AI_COMPLETE,
    isUser: false,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  },
])

const aiApi = uniCloud.importObject('ai', {
  customUI: true, // 取消自动展示的交互提示界面
})

// 全新的 AI 状态管理 - 前后端一体化重构
const aiState = ref({
  // 会话状态
  sessionId: null,
  isProcessing: false,

  // 加载状态
  loading: {
    show: false,
    text: '',
    stage: '', // thinking/analyzing/preparing/executing/completing
    progress: null,
  },

  // 流式消息状态
  streaming: {
    active: false,
    messageId: null,
    intentType: null,
  },

  // Function Calling 专用状态
  toolExecution: {
    active: false, // 是否正在执行工具
    currentTool: null, // 当前执行的工具名称
    executedTools: [], // 已执行的工具列表
    results: new Map(), // 工具执行结果缓存
  },
})

// 连接状态管理
const connectionStatus = ref('disconnected') // connecting/connected/error
const retryCount = ref(0)
const maxRetries = 3

// 状态控制函数
const showLoading = (text, stage = 'thinking') => {
  aiState.value.loading = {
    show: true,
    text,
    stage,
    progress: null,
  }
}

const updateLoading = (updates) => {
  if (aiState.value.loading.show) {
    Object.assign(aiState.value.loading, updates)
    // 移除手动滚动调用，让组件内部的监听器处理
  }
}

const hideLoading = () => {
  aiState.value.loading.show = false
}

const resetAiState = () => {
  const prevSession = aiState.value.sessionId
  aiState.value = {
    sessionId: null,
    isProcessing: false,
    loading: {
      show: false,
      text: '',
      stage: '',
      progress: null,
    },
    streaming: {
      active: false,
      messageId: null,
      intentType: null,
    },
    toolExecution: {
      active: false,
      currentTool: null,
      executedTools: [],
      results: new Map(),
    },
  }
  console.log('[打断] 已复位前端状态', { 上次会话: prevSession })
}

onUnmounted(() => {
  // 组件卸载时重置状态
  resetAiState()
})

// 消息操作函数
const addUserMessage = (content) => {
  const userMessage = {
    _id: `user_${Date.now()}`,
    type: MESSAGE_TYPES.USER,
    content,
    isUser: true,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }
  messages.value.push(userMessage)
  // 移除手动滚动调用，让组件内部的监听器处理
}

const createStreamingMessage = () => {
  const streamingMessage = {
    _id: `streaming_${Date.now()}`,
    type: MESSAGE_TYPES.AI_STREAMING,
    content: '',
    isUser: false,
    status: MESSAGE_STATUS.STREAMING,
    time: new Date().toISOString(),
  }
  messages.value.push(streamingMessage)
  aiState.value.streaming.active = true
  aiState.value.streaming.messageId = streamingMessage._id
  // 移除手动滚动调用，让组件内部的监听器处理
}

const appendStreamingContent = (content) => {
  if (aiState.value.streaming.messageId) {
    const messageIndex = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex].content += content
      // 流式内容更新时需要立即滚动，保持跟随最新内容
      nextTick(() => {
        messageListRef.value?.scrollToBottom()
      })
    }
  }
}

const finalizeStreamingMessage = () => {
  if (aiState.value.streaming.messageId) {
    const messageIndex = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
    if (messageIndex !== -1) {
      messages.value[messageIndex].status = MESSAGE_STATUS.COMPLETE
      messages.value[messageIndex].type = MESSAGE_TYPES.AI_COMPLETE
    }
  }
}

// 终止当前流式消息：用于打断或服务端 end(canceled)
const finalizeStreamingWithReason = (reason) => {
  if (!aiState.value.streaming.messageId) return
  const idx = messages.value.findIndex((msg) => msg._id === aiState.value.streaming.messageId)
  if (idx === -1) return
  // 标记为完成，移除闪烁光标
  messages.value[idx].status = MESSAGE_STATUS.COMPLETE
  messages.value[idx].type = MESSAGE_TYPES.AI_COMPLETE
  // 如为取消，追加标注（避免重复追加）
  if (reason === 'canceled') {
    const tag = '（已终止）'
    if (!String(messages.value[idx].content || '').endsWith(tag)) {
      messages.value[idx].content = (messages.value[idx].content || '') + tag
    }
  }
}

// 已废弃：任务完成气泡逻辑（保留期间已不再使用）

const addErrorMessage = (error) => {
  const errorMessage = {
    _id: `error_${Date.now()}`,
    type: MESSAGE_TYPES.ERROR,
    content: `抱歉，处理过程中出现错误：${error}`,
    isUser: false,
    status: MESSAGE_STATUS.ERROR,
    time: new Date().toISOString(),
  }
  messages.value.push(errorMessage)
  // 移除手动滚动调用，让组件内部的监听器处理
}

/**
 * 添加工具执行结果消息
 * @param {Object} options - 工具结果选项
 * @param {string} options.toolName - 工具名称
 * @param {Object} options.result - 执行结果
 * @param {boolean} options.success - 是否成功
 * @param {string} options.error - 错误信息
 * @param {string} options.toolCallId - 工具调用 ID
 */
const addToolResultMessage = ({ toolName, result, success, error, toolCallId }) => {
  console.log('📝 创建工具结果消息：', {
    toolName,
    success,
    hasResult: !!result,
    hasError: !!error,
    toolCallId,
  })

  const toolResultMessage = {
    _id: `tool_result_${Date.now()}`,
    type: MESSAGE_TYPES.TOOL_RESULT,
    content: success
      ? `${toolName} 执行成功${result?.message ? '：' + result.message : ''}`
      : `${toolName} 执行失败${error ? '：' + error : ''}`,
    toolName: toolName,
    result: success ? result : null,
    error: error || null,
    success: success,
    toolCallId: toolCallId || `tool_${toolName}_${Date.now()}`,
    isUser: false,
    status: success ? MESSAGE_STATUS.COMPLETE : MESSAGE_STATUS.ERROR,
    time: new Date().toISOString(),
    // 工具结果专用字段
    toolData: {
      executionTime: result?.executionTime || null,
      dataCount: result?.dataCount || 0,
      summary: result?.message || (success ? '执行成功' : '执行失败'),
    },
  }

  console.log('📋 工具结果消息对象：', toolResultMessage)

  messages.value.push(toolResultMessage)

  console.log('📊 消息添加后状态：', {
    totalMessages: messages.value.length,
    lastMessage: messages.value[messages.value.length - 1],
    messageTypes: messages.value.map((m) => m.type),
  })

  // 更新工具执行状态
  aiState.value.toolExecution.executedTools.push({
    name: toolName,
    success: success,
    timestamp: new Date().toISOString(),
  })

  if (success && result) {
    aiState.value.toolExecution.results.set(toolName, result)
  }

  // 移除手动滚动调用，让组件内部的监听器处理
  console.log('🔄 工具结果消息已添加')
}

/**
 * 更新工具执行状态
 * @param {string} toolName - 工具名称
 * @param {string} status - 状态：'start' | 'executing' | 'complete' | 'error'
 */
const updateToolExecutionState = (toolName, status) => {
  switch (status) {
    case 'start':
      aiState.value.toolExecution.active = true
      aiState.value.toolExecution.currentTool = toolName
      break
    case 'complete':
    case 'error':
      aiState.value.toolExecution.active = false
      aiState.value.toolExecution.currentTool = null
      break
  }
}

// 添加任务确认消息的辅助函数（保持兼容性）
const addTaskConfirmMessage = (recognizedContent) => {
  const taskConfirmMessage = {
    _id: `task_confirm_${Date.now()}`,
    type: 'task-confirm',
    recognizedContent,
    isUser: false,
    time: new Date().toISOString(),
  }
  messages.value.push(taskConfirmMessage)
  // 移除手动滚动调用，让组件内部的监听器处理
}

// 消息流程跟踪
const messageFlow = ref([])

// 全新的流式消息处理逻辑
const handleStreamMessage = (message) => {
  // 记录消息流程
  messageFlow.value.push({
    timestamp: new Date().toISOString(),
    type: message.type,
    data: message.data,
    sessionId: message.sessionId,
  })

  console.log('🔔 收到流式消息：', {
    type: message.type,
    hasData: !!message.data,
    hasSessionId: !!message.sessionId,
    messageKeys: Object.keys(message),
    fullMessage: message,
    messageFlowCount: messageFlow.value.length,
  })

  const { type, data, sessionId, timestamp } = message

  // 验证会话 ID（对于执行器推送的消息，可能没有 sessionId）
  if (aiState.value.sessionId && sessionId && sessionId !== aiState.value.sessionId) {
    console.warn('收到不匹配的会话消息，忽略', { expected: aiState.value.sessionId, received: sessionId })
    return
  }

  switch (type) {
    case SSE_MESSAGE_TYPES.PROCESSING_START:
      aiState.value.sessionId = sessionId
      aiState.value.isProcessing = true
      showLoading(data.message, 'thinking')
      break

    case SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK:
      // 首次收到内容时，切换到流式显示
      console.log('💬 收到聊天内容块：', {
        content: data.content,
        isComplete: data.isComplete,
        streamingActive: aiState.value.streaming.active,
        loadingShow: aiState.value.loading.show,
      })

      if (!aiState.value.streaming.active) {
        console.log('🔄 开始流式显示，隐藏加载状态')
        hideLoading()
        createStreamingMessage()
      }
      appendStreamingContent(data.content)

      // 如果内容完成，结束流式显示
      if (data.isComplete) {
        console.log('✅ 聊天内容完成，结束流式显示')
        finalizeStreamingMessage()
        resetAiState()
      }
      break

    case SSE_MESSAGE_TYPES.TOOL_CALL_START:
      // 工具调用开始
      console.log('🛠️ 工具调用开始：', { toolName: data.toolName, toolCallId: data.toolCallId, sessionId })
      hideLoading()
      updateToolExecutionState(data.toolName, 'start')
      showLoading(`准备执行工具：${data.toolName}`, 'executing')
      break

    case SSE_MESSAGE_TYPES.TOOL_EXECUTION_START:
      // 工具执行开始
      console.log('🚀 工具执行开始：', { toolName: data.toolName, parameters: data.parameters, sessionId })
      updateToolExecutionState(data.toolName, 'executing')
      updateLoading({
        text: `正在执行：${data.toolName}`,
        stage: 'executing',
      })
      break

    case SSE_MESSAGE_TYPES.TOOL_EXECUTION_COMPLETE:
      // 工具执行完成
      console.log('🔧 工具执行完成：', {
        toolName: data.toolName,
        result: data.result,
        toolCallId: data.toolCallId,
        currentMessagesCount: messages.value.length,
      })

      updateToolExecutionState(data.toolName, 'complete')

      // 添加工具执行结果消息
      addToolResultMessage({
        toolName: data.toolName,
        result: data.result,
        success: true,
        toolCallId: data.toolCallId,
      })

      console.log('✅ 工具结果消息已添加，当前消息数量：', messages.value.length)

      // 不立即隐藏 loading，等待后续的 AI 回复
      updateLoading({
        text: '工具执行完成，正在生成回复...',
        stage: 'processing',
      })
      break

    case SSE_MESSAGE_TYPES.TOOL_EXECUTION_ERROR:
      // 工具执行失败
      updateToolExecutionState(data.toolName, 'error')

      // 添加工具执行错误消息
      addToolResultMessage({
        toolName: data.toolName,
        error: data.error,
        success: false,
        toolCallId: data.toolCallId,
      })

      // 继续等待 AI 的错误处理回复
      updateLoading({
        text: '工具执行失败，正在处理...',
        stage: 'processing',
      })
      break

    case SSE_MESSAGE_TYPES.TOOL_RESULT_PROCESSING:
      // 工具结果处理中
      console.log('🔄 工具结果处理中，重置流式状态')
      // 重置流式状态，为后续的 AI 回复做准备
      aiState.value.streaming.active = false
      aiState.value.streaming.messageId = null

      updateLoading({
        text: data.message || '正在处理工具执行结果...',
        stage: 'processing',
      })
      break

    case SSE_MESSAGE_TYPES.TOOL_RESULT_ERROR:
      // 工具结果处理失败
      hideLoading()
      addErrorMessage(data.error || '工具结果处理失败')
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.ERROR:
      hideLoading()
      // 直接使用后端推送的错误信息
      addErrorMessage(data.error)
      resetAiState()
      break

    case SSE_MESSAGE_TYPES.SESSION_END:
      console.log('🔚 会话结束，清理状态')
      // 若仍处于流式中，但服务端直接结束（可能是取消或异常），需要手动把流式气泡定格，去掉光标
      if (aiState.value.streaming.active) {
        const reason = data?.reason
        finalizeStreamingWithReason(reason)
      }
      hideLoading()
      resetAiState()
      break

    default:
      console.warn('❌ 未知的消息类型：', {
        type,
        expectedTypes: Object.values(SSE_MESSAGE_TYPES),
        fullMessage: message,
        isTypeInExpected: Object.values(SSE_MESSAGE_TYPES).includes(type),
      })
  }
}

// 全新的发送消息流程
const handleSendMessageStream = async () => {
  if (!inputValue.value.trim() || aiState.value.isProcessing) return

  // 1. 添加用户消息
  addUserMessage(inputValue.value)

  // 2. 清空输入框
  const userMessage = inputValue.value
  inputValue.value = ''

  // 3. 立即显示 loading 气泡
  showLoading('正在思考中...', 'thinking')

  // 4. 发送请求（移除手动滚动调用，让组件内部的监听器处理）
  await sendMessageToAI(userMessage)
}

/**
 * 将本地消息格式转换为 Function Calling 标准格式
 * @param {Array} messages - 本地消息数组
 * @returns {Array} - Function Calling 格式的消息数组
 */
const convertMessagesToFunctionCallingFormat = (messages) => {
  return messages
    .filter(
      (msg) =>
        msg.type === MESSAGE_TYPES.USER ||
        msg.type === MESSAGE_TYPES.AI_COMPLETE ||
        msg.type === MESSAGE_TYPES.TOOL_RESULT
    )
    .map((msg) => {
      // 用户消息
      if (msg.isUser) {
        return {
          role: 'user',
          content: msg.content,
        }
      }

      // AI 完成消息（可能包含工具调用）
      if (msg.type === MESSAGE_TYPES.AI_COMPLETE) {
        const assistantMessage = {
          role: 'assistant',
          content: msg.content,
        }

        // 如果消息包含工具调用信息，添加到消息中
        if (msg.tool_calls && msg.tool_calls.length > 0) {
          assistantMessage.tool_calls = msg.tool_calls
        }

        return assistantMessage
      }

      // 工具执行结果消息
      if (msg.type === MESSAGE_TYPES.TOOL_RESULT) {
        return {
          role: 'tool',
          tool_call_id: msg.toolCallId,
          content: JSON.stringify({
            success: msg.success,
            result: msg.result,
            error: msg.error,
            toolName: msg.toolName,
            executionTime: msg.toolData?.executionTime,
            summary: msg.toolData?.summary,
          }),
        }
      }
    })
    .filter(Boolean) // 过滤掉 undefined 值
}

// 发送消息到 AI 的核心函数
const sendMessageToAI = async (userMessage) => {
  try {
    // 清理上一次会话 ID，避免新会话的 PROCESSING_START/CHUNK 被旧 sessionId 过滤
    aiState.value.sessionId = null
    const clientReqId = `req_${Date.now()}_${Math.random().toString(36).slice(2, 7)}`
    lastClientReqId.value = clientReqId
    console.log('[打断] 发起请求', { 请求ID: clientReqId, 文本长度: userMessage?.length || 0 })
    const channel = new uniCloud.SSEChannel()
    currentChannel.value = channel

    channel.on('open', () => {
      console.log('[打断] SSE 通道已建立', { 请求ID: clientReqId })
      connectionStatus.value = 'connected'
    })

    channel.on('message', (data) => {
      if (data?.type === SSE_MESSAGE_TYPES.PROCESSING_START) {
        console.log('[打断] 收到开始事件', { 请求ID: clientReqId, 会话ID: data?.sessionId })
      }
      if (
        !hasFirstMessage.value &&
        (data?.type === SSE_MESSAGE_TYPES.PROCESSING_START || data?.type === SSE_MESSAGE_TYPES.CHAT_CONTENT_CHUNK)
      ) {
        hasFirstMessage.value = true
        if (firstMessageTimer) {
          clearTimeout(firstMessageTimer)
          firstMessageTimer = null
        }
        // 首条消息到达，仅内部状态，不额外打印
      }
      handleStreamMessage(data)
    })

    channel.on('end', (data) => {
      console.log('[打断] 收到结束事件', { 请求ID: clientReqId, 结束原因: data?.data?.reason || '未知' })
      if (data) {
        handleStreamMessage(data)
      }
      connectionStatus.value = 'disconnected'
      currentChannel.value = null
    })

    channel.on('error', (error) => {
      console.error('[打断] SSE 通道错误', { 请求ID: clientReqId, 错误: error?.message })
      connectionStatus.value = 'error'
      currentChannel.value = null
      handleStreamMessage({
        type: SSE_MESSAGE_TYPES.ERROR,
        data: { error: error.message || '连接错误' },
      })
    })

    await channel.open()
    console.log('[打断] SSE 通道已就绪', { 请求ID: clientReqId })
    // 启动首包看门狗：3.5s 未收到首条消息则打点日志
    hasFirstMessage.value = false
    if (firstMessageTimer) clearTimeout(firstMessageTimer)
    firstMessageTimer = setTimeout(() => {
      if (!hasFirstMessage.value) {
        console.warn('[打断] 警告：3.5s 未收到首条 SSE 消息，可能卡住', {
          请求ID: clientReqId,
          通道状态: connectionStatus.value,
        })
      }
    }, 3500)

    // 使用 Function Calling 标准格式转换历史消息
    const historyMessages = convertMessagesToFunctionCallingFormat(messages.value)

    console.log('发送历史消息（Function Calling 格式）：', {
      messageCount: historyMessages.length,
      hasToolResults: historyMessages.some((msg) => msg.role === 'tool'),
      hasToolCalls: historyMessages.some((msg) => msg.tool_calls),
      messages: historyMessages,
    })

    // 简化：不打印详细历史，仅标记发起
    console.log('[打断] 调用后端开始流式', { 请求ID: clientReqId })
    const response = await aiApi.chatStreamSSE({
      message: userMessage,
      messages: historyMessages,
      channel: channel,
    })
    console.log('[打断] 后端返回', { 请求ID: clientReqId, 结果码: response?.errCode })

    if (response.errCode !== 0) {
      throw new Error(response.errMsg || '调用 AI 接口失败')
    }
  } catch (error) {
    console.error('[打断] 发送失败', { 错误: error?.message, 请求ID: lastClientReqId.value })
    connectionStatus.value = 'error'
    currentChannel.value = null
    handleStreamMessage({
      type: SSE_MESSAGE_TYPES.ERROR,
      data: { error: error.message },
    })
  }
}

const handleSendAudio = (audioData) => {
  // 添加音频消息
  const audioMessage = {
    _id: `audio_${Date.now()}`,
    type: 'audio',
    isUser: true,
    audioUrl: audioData.tempFileURL,
    audioDuration: audioData.duration,
    status: MESSAGE_STATUS.COMPLETE,
    time: new Date().toISOString(),
  }
  messages.value.push(audioMessage)

  // 显示加载状态 - 注意：这里是临时的模拟实现
  // 实际应该调用后端语音处理接口，并通过 SSE 接收状态更新
  showLoading('处理语音中...', 'thinking')

  setTimeout(() => {
    hideLoading()
    const responseMessage = {
      _id: `response_${Date.now()}`,
      content: '我收到了你的语音，正在思考如何回复...',
      type: MESSAGE_TYPES.AI_COMPLETE,
      isUser: false,
      status: MESSAGE_STATUS.COMPLETE,
      time: new Date().toISOString(),
    }
    messages.value.push(responseMessage)
    // 移除手动滚动调用，让组件内部的监听器处理
  }, 1500)
}

// 任务确认处理函数
const handleTaskConfirm = ({ messageId, content }) => {
  console.log('任务确认：', { messageId, content })

  // 找到对应的消息并转换为普通文本消息
  const messageIndex = messages.value.findIndex((msg) => msg._id === messageId)
  if (messageIndex !== -1) {
    messages.value[messageIndex] = {
      ...messages.value[messageIndex],
      type: 'text',
      content: `任务已确认：${content}`,
      isUser: false,
      time: new Date().toISOString(),
    }
  }

  // 这里可以添加确认后的处理逻辑，比如实际创建任务
}

const handleTaskCancel = ({ messageId }) => {
  console.log('任务取消：', messageId)

  // 找到对应的消息并转换为普通文本消息
  const messageIndex = messages.value.findIndex((msg) => msg._id === messageId)
  if (messageIndex !== -1) {
    messages.value[messageIndex] = {
      ...messages.value[messageIndex],
      type: 'text',
      content: '任务已取消',
      isUser: false,
      time: new Date().toISOString(),
    }
  }

  // 这里可以添加取消后的处理逻辑
}

// 跳转到首页
const goToHome = () => {
  router.push('/pages/index/index')
}

// 停止流式生成：本地关闭 + 通知服务端取消
const handleStop = async () => {
  try {
    const sessionId = aiState.value.sessionId
    console.log('[打断] 用户点击停止', {
      会话ID: sessionId,
      持有通道: !!currentChannel.value,
      请求ID: lastClientReqId.value,
    })
    // 先本地立即关闭通道，停止显示新增内容
    if (currentChannel.value) {
      try {
        currentChannel.value.close()
        console.log('[打断] 本地通道已关闭')
      } catch (_) {}
      currentChannel.value = null
    }
    // 通知服务端取消（若已拿到会话 ID）
    if (sessionId) {
      const res = await aiApi.cancelChat({ sessionId })
      console.log('[打断] 服务端取消结果', { 结果码: res?.errCode })
    }
  } catch (e) {
    console.warn('停止失败（已兜底本地关闭）：', e?.message)
  } finally {
    // 本地彻底复位，防止保留旧的 sessionId 影响下一轮消息过滤
    // 若当前仍有流式气泡，先定格以去除闪烁光标
    finalizeStreamingWithReason('canceled')
    hideLoading()
    resetAiState()
  }
}

// 清空聊天弹窗控制
const showClearConfirm = ref(false)

// 触发清空确认弹窗
const clearChatHistory = () => {
  showClearConfirm.value = true
}

// 确认清空
const doClearChatHistory = async () => {
  try {
    if (aiState.value.isProcessing || aiState.value.streaming.active) {
      try {
        await handleStop()
      } catch (_) {}
    }
    messages.value = []
    resetAiState()
    // 清空后不需要滚动，因为没有内容
  } catch (e) {
    console.warn('清空聊天记录失败：', e?.message)
  }
}
</script>

<style lang="scss" scoped>
.ai-chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f4f5f7;
  padding-bottom: 110px;
    /* 为固定底部输入框预留空间 */
}

/* 使用组件内固定，wrapper 仅作占位，无需 fixed 样式 */
.message-input-wrapper {
  background-color: #fff;
}

.channel-status-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  padding: 8px 16px;

  .status-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      animation: pulse 1.5s infinite;
    }

    .status-text {
      color: #fff;
      font-size: 12px;
    }

    &.connecting .status-dot {
      background-color: #ffa500;
    }

    &.error .status-dot {
      background-color: #ff4757;
    }

    &.disconnected .status-dot {
      background-color: #747d8c;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

/* 首页按钮样式 */
.home-button {
  position: fixed;
  bottom: 120px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: var(--color-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.home-button:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.home-button i {
  font-size: 16px;
}

/* 停止按钮样式 */
.stop-button {
  position: fixed;
  bottom: 70px;
  right: 20px;
  width: 40px;
  height: 40px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  cursor: pointer;
  box-shadow: var(--shadow-sm);
  transition: var(--transition-fast);
}

.stop-button:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}
</style>
